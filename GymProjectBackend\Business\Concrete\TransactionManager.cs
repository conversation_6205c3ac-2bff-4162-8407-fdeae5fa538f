﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class TransactionManager : ITransactionService
    {
        private readonly ITransactionDal _transactionDal;
        private readonly IMemberDal _memberDal;
        private readonly ICompanyContext _companyContext;

        public TransactionManager(ITransactionDal transactionDal, IMemberDal memberDal, ICompanyContext companyContext)
        {
            _transactionDal = transactionDal;
            _memberDal = memberDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult AddBulk(BulkTransactionDto bulkTransaction)
        {
            // SOLID Refactoring: Delegate complex business logic to DAL
            // Exact same functionality preserved
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.AddBulkTransactionWithBusinessLogic(bulkTransaction, companyId);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult Add(Transaction transaction)
        {
            // SOLID Refactoring: Delegate complex business logic to DAL
            // Exact same functionality preserved
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.AddTransactionWithBusinessLogic(transaction, companyId);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult UpdatePaymentStatus(int transactionId)
        {
            // SOLID Refactoring: Delegate complex business logic to DAL
            // Exact same functionality preserved
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.UpdatePaymentStatusWithBusinessLogic(transactionId, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult UpdateAllPaymentStatus(int memberId)
        {
            // SOLID Refactoring: Delegate complex business logic to DAL
            // Exact same functionality preserved
            var companyId = _companyContext.GetCompanyId();
            return _transactionDal.UpdateAllPaymentStatusWithBusinessLogic(memberId, companyId);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "All")]
        public IDataResult<List<Transaction>> GetAll()
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "ByMember")]
        public IDataResult<List<Transaction>> GetByMemberId(int memberId)
        {
            return new SuccessDataResult<List<Transaction>>(_transactionDal.GetAll(t => t.MemberID == memberId));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "WithDetails")]
        public IDataResult<List<TransactionDetailDto>> GetTransactionsWithDetails()
        {
            return new SuccessDataResult<List<TransactionDetailDto>>(_transactionDal.GetTransactionsWithDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Transaction", "Unpaid")]
        public IDataResult<List<TransactionDetailDto>> GetUnpaidTransactions(int memberId)
        {
            var transactions = _transactionDal.GetTransactionsWithDetails();
            return new SuccessDataResult<List<TransactionDetailDto>>(
                transactions.Where(t => t.MemberID == memberId && !t.IsPaid).ToList()
            );
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Transaction")]
        public IResult Delete(int transactionId)
        {
            var transaction = _transactionDal.Get(t => t.TransactionID == transactionId && t.CompanyID == _companyContext.GetCompanyId());
            if (transaction == null)
                return new ErrorResult("İşlem bulunamadı veya erişim yetkiniz yok.");

            // Sadece ödenmiş işlemler silinebilir
            if (!transaction.IsPaid)
                return new ErrorResult("Sadece ödenmiş işlemler silinebilir.");

            // Soft delete
            transaction.IsActive = false;
            transaction.DeletedDate = DateTime.Now;
            transaction.UpdatedDate = DateTime.Now;

            _transactionDal.Update(transaction);
            return new SuccessResult("İşlem başarıyla silindi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Transaction", "Monthly")]
        public IDataResult<decimal> GetMonthlyTransactionTotal(int year, int month)
        {
            var monthStart = new DateTime(year, month, 1);
            var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

            var allTransactions = _transactionDal.GetTransactionsWithDetails();

            var monthlyTransactions = allTransactions
                .Where(t => t.TransactionDate >= monthStart &&
                           t.TransactionDate <= monthEnd &&
                           t.TransactionType != "Bakiye Yükleme")
                .ToList();

            var totalAmount = monthlyTransactions.Sum(t => t.TotalPrice);

            return new SuccessDataResult<decimal>(totalAmount, "Aylık işlem toplamı başarıyla getirildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Transaction", "Daily")]
        public IDataResult<decimal> GetDailyTransactionTotal(DateTime date)
        {
            var dayStart = date.Date;
            var dayEnd = dayStart.AddDays(1).AddTicks(-1);

            var allTransactions = _transactionDal.GetTransactionsWithDetails();

            var dailyTransactions = allTransactions
                .Where(t => t.TransactionDate >= dayStart &&
                           t.TransactionDate <= dayEnd &&
                           t.TransactionType != "Bakiye Yükleme")
                .ToList();

            var totalAmount = dailyTransactions.Sum(t => t.TotalPrice);

            return new SuccessDataResult<decimal>(totalAmount, "Günlük işlem toplamı başarıyla getirildi.");
        }
    }
}