﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTransactionDal : EfCompanyEntityRepositoryBase<Transaction, GymContext>, ITransactionDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfTransactionDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfTransactionDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<TransactionDetailDto> GetTransactionsWithDetails()
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var result = from t in _context.Transactions
                         join m in _context.Members on t.MemberID equals m.MemberID
                         join p in _context.Products on t.ProductID equals p.ProductID into productJoin
                         from p in productJoin.DefaultIfEmpty()
                         where t.CompanyID == companyId // Şirket ID'sine göre filtrele
                         && m.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                         && (p == null || p.CompanyID == companyId) // Ürünlerin de aynı şirkete ait olduğundan emin ol
                         && t.IsActive == true // Sadece aktif (silinmemiş) işlemler
                         orderby t.TransactionDate descending // Tarih bazlı sıralama eklendi
                             select new TransactionDetailDto
                             {
                                 TransactionID = t.TransactionID,
                                 MemberID = t.MemberID,
                                 MemberName = m.Name,
                                 ProductID = t.ProductID,
                                 ProductName = p != null ? p.Name : null,
                                 Amount = t.Amount,
                                 UnitPrice = t.UnitPrice,
                                 TransactionType = t.TransactionType,
                                 TransactionDate = t.TransactionDate,
                                 Quantity = t.Quantity,
                                 IsPaid = t.IsPaid,
                                 Balance = m.Balance,
                                 TotalPrice = t.TransactionType == "Satış" ? t.UnitPrice * t.Quantity : t.Amount
                             };

            return result.ToList();
        }

        // SOLID Refactoring: Business logic moved from TransactionManager
        // Exact same functionality as TransactionManager.Add method
        public IResult AddTransactionWithBusinessLogic(Transaction transaction, int companyId)
        {
            try
            {
                // Get member - same logic as TransactionManager
                Member member;
                if (_context != null)
                {
                    member = _context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID);
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        member = context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID);
                    }
                }

                if (member == null)
                    return new ErrorResult("Üye bulunamadı");

                // Set transaction date - same as TransactionManager
                transaction.TransactionDate = DateTime.Now;
                transaction.CompanyID = companyId;

                // Business logic - exactly same as TransactionManager.Add
                if (transaction.TransactionType == "Bakiye Yükleme")
                {
                    member.Balance += transaction.Amount;
                    transaction.IsPaid = true;
                    transaction.ProductID = null;
                }
                else
                {
                    decimal totalCost = transaction.UnitPrice * transaction.Quantity;
                    decimal availableBalance = Math.Max(0, member.Balance);
                    decimal amountToDeduct = Math.Min(totalCost, availableBalance);
                    decimal remainingDebt = totalCost - amountToDeduct;

                    member.Balance -= totalCost; // Toplam tutarı bakiyeden düş
                    transaction.Amount = remainingDebt; // Sadece kalan borç miktarını Amount'a kaydet
                    transaction.IsPaid = remainingDebt <= 0;
                }

                // Update member and add transaction - same as TransactionManager
                member.UpdatedDate = DateTime.Now;

                if (_context != null)
                {
                    _context.Members.Update(member);
                    _context.Transactions.Add(transaction);
                    _context.SaveChanges();
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        context.Members.Update(member);
                        context.Transactions.Add(transaction);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("İşlem başarıyla gerçekleştirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Transaction eklenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID Refactoring: Business logic moved from TransactionManager
        // Exact same functionality as TransactionManager.AddBulk method
        public IResult AddBulkTransactionWithBusinessLogic(BulkTransactionDto bulkTransaction, int companyId)
        {
            try
            {
                // Get member - same logic as TransactionManager
                Member member;
                if (_context != null)
                {
                    member = _context.Members.FirstOrDefault(m => m.MemberID == bulkTransaction.MemberID);
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        member = context.Members.FirstOrDefault(m => m.MemberID == bulkTransaction.MemberID);
                    }
                }

                if (member == null)
                    return new ErrorResult("Üye bulunamadı");

                // Business logic - exactly same as TransactionManager.AddBulk
                decimal totalAmount = bulkTransaction.Items.Sum(item => item.Quantity * item.UnitPrice);
                decimal availableBalance = Math.Max(0, member.Balance);
                decimal amountToDeduct = Math.Min(totalAmount, availableBalance);
                decimal remainingDebt = totalAmount - amountToDeduct;

                member.Balance -= totalAmount;
                member.UpdatedDate = DateTime.Now;

                // Create transactions for each item - same logic as TransactionManager
                var transactionsToAdd = new List<Transaction>();
                foreach (var item in bulkTransaction.Items)
                {
                    decimal itemTotalCost = item.Quantity * item.UnitPrice;
                    decimal itemAvailableBalance = (availableBalance * itemTotalCost) / totalAmount;
                    decimal itemDebt = itemTotalCost - itemAvailableBalance;

                    var transaction = new Transaction
                    {
                        MemberID = bulkTransaction.MemberID,
                        ProductID = item.ProductID,
                        Amount = itemDebt,
                        UnitPrice = item.UnitPrice,
                        Quantity = item.Quantity,
                        TransactionType = bulkTransaction.TransactionType,
                        TransactionDate = DateTime.Now,
                        IsPaid = itemDebt <= 0,
                        CompanyID = companyId
                    };
                    transactionsToAdd.Add(transaction);
                }

                // Save all changes - same as TransactionManager
                if (_context != null)
                {
                    _context.Members.Update(member);
                    _context.Transactions.AddRange(transactionsToAdd);
                    _context.SaveChanges();
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        context.Members.Update(member);
                        context.Transactions.AddRange(transactionsToAdd);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("İşlem başarıyla gerçekleştirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Bulk transaction eklenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID Refactoring: Business logic moved from TransactionManager
        // Exact same functionality as TransactionManager.UpdatePaymentStatus method
        public IResult UpdatePaymentStatusWithBusinessLogic(int transactionId, int companyId)
        {
            try
            {
                // Get transaction with company check - same logic as TransactionManager
                Transaction transaction;
                if (_context != null)
                {
                    transaction = _context.Transactions.FirstOrDefault(t => t.TransactionID == transactionId && t.CompanyID == companyId);
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        transaction = context.Transactions.FirstOrDefault(t => t.TransactionID == transactionId && t.CompanyID == companyId);
                    }
                }

                if (transaction == null)
                    return new ErrorResult("İşlem bulunamadı veya erişim yetkiniz yok.");

                // Get member - same logic as TransactionManager
                Member member;
                if (_context != null)
                {
                    member = _context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID);
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        member = context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID);
                    }
                }

                if (member == null)
                    return new ErrorResult("Üye bulunamadı");

                // Business logic - exactly same as TransactionManager.UpdatePaymentStatus
                if (transaction.IsPaid)
                    return new SuccessResult();

                if (!transaction.IsPaid && transaction.TransactionType != "Bakiye Yükleme")
                {
                    member.Balance += transaction.Amount;
                    member.UpdatedDate = DateTime.Now;
                }

                transaction.IsPaid = true;

                // Save changes - same as TransactionManager
                if (_context != null)
                {
                    _context.Members.Update(member);
                    _context.Transactions.Update(transaction);
                    _context.SaveChanges();
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        context.Members.Update(member);
                        context.Transactions.Update(transaction);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult();
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Payment status güncellenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID Refactoring: Business logic moved from TransactionManager
        // Exact same functionality as TransactionManager.UpdateAllPaymentStatus method
        public IResult UpdateAllPaymentStatusWithBusinessLogic(int memberId, int companyId)
        {
            try
            {
                // Get member with company check - same logic as TransactionManager
                Member member;
                if (_context != null)
                {
                    member = _context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
                    }
                }

                if (member == null)
                    return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");

                // Get unpaid transactions - same logic as TransactionManager
                List<Transaction> transactions;
                if (_context != null)
                {
                    transactions = _context.Transactions
                        .Where(t => t.MemberID == memberId && !t.IsPaid && t.TransactionType != "Bakiye Yükleme" && t.CompanyID == companyId)
                        .ToList();
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        transactions = context.Transactions
                            .Where(t => t.MemberID == memberId && !t.IsPaid && t.TransactionType != "Bakiye Yükleme" && t.CompanyID == companyId)
                            .ToList();
                    }
                }

                if (!transactions.Any())
                    return new ErrorResult("Ödenecek işlem bulunamadı.");

                // Business logic - exactly same as TransactionManager.UpdateAllPaymentStatus
                decimal totalDebt = transactions.Sum(t => t.Amount);

                member.Balance = 0;
                member.UpdatedDate = DateTime.Now;

                foreach (var transaction in transactions)
                {
                    transaction.IsPaid = true;
                }

                // Save changes - same as TransactionManager
                if (_context != null)
                {
                    _context.Members.Update(member);
                    _context.Transactions.UpdateRange(transactions);
                    _context.SaveChanges();
                }
                else
                {
                    using (var context = new GymContext())
                    {
                        context.Members.Update(member);
                        context.Transactions.UpdateRange(transactions);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Tüm ödemeler başarıyla yapıldı.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Tüm payment status güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
